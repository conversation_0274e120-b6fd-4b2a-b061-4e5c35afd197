services:
  lobe-chat:
    image: lobehub/lobe-chat:latest
    container_name: ${CONTAINER_NAME}
    restart: always
    networks:
      - 1panel-network
    ports:
      - "${PANEL_APP_PORT_HTTP}:3210"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_PROXY_URL=${OPENAI_PROXY_URL}
      - ACCESS_CODE=${ACCESS_CODE}
      - OPENAI_MODEL_LIST=${OPENAI_MODEL_LIST}
    labels:  
      createdBy: "Apps"

networks:  
  1panel-network:  
    external: true
