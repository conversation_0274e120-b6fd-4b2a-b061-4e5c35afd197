# ===========================================
# MoonTV 环境变量配置文件
# ===========================================

# ===========================================
# 基础认证配置
# ===========================================
# 管理员用户名）
USERNAME=admin

# 管理员密码（必填，建议使用强密码）
PASSWORD=admin

# ===========================================
# 网站基本配置
# ===========================================
# 网站名称
NEXT_PUBLIC_SITE_NAME=MoonTV

# 网站公告
ANNOUNCEMENT=本网站仅提供影视信息搜索服务，所有内容均来自第三方网站。本站不存储任何视频资源，不对任何内容的准确性、合法性、完整性负责。

# 搜索接口可拉取的最大页数（1-50）
NEXT_PUBLIC_SEARCH_MAX_PAGE=5

# ===========================================
# 存储配置
# ===========================================
# 播放记录/收藏的存储方式
# 可选值: redis、upstash
NEXT_PUBLIC_STORAGE_TYPE=redis

# Redis 连接 URL（当 STORAGE_TYPE=redis 时需要）
REDIS_URL=redis://localhost:6379

# Upstash Redis 配置（当 STORAGE_TYPE=upstash 时需要）
# UPSTASH_URL=https://your-upstash-endpoint.upstash.io
# UPSTASH_TOKEN=your_upstash_token

# ===========================================
# 用户注册配置
# ===========================================
# 是否开放注册
NEXT_PUBLIC_ENABLE_REGISTER=false

# ===========================================
# 豆瓣数据源配置
# ===========================================
# 豆瓣数据源请求方式
# 可选值: direct、cors-proxy-zwei、cmliussss-cdn-tencent、cmliussss-cdn-ali、cors-anywhere、custom
# NEXT_PUBLIC_DOUBAN_PROXY_TYPE=direct

# 自定义豆瓣数据代理 URL（当 DOUBAN_PROXY_TYPE=custom 时需要）
# NEXT_PUBLIC_DOUBAN_PROXY=https://your-douban-proxy.com

# ===========================================
# 豆瓣图片代理配置
# ===========================================
# 豆瓣图片代理类型
# 可选值: direct、server、img3、cmliussss-cdn-tencent、cmliussss-cdn-ali、custom
# NEXT_PUBLIC_DOUBAN_IMAGE_PROXY_TYPE=direct

# 自定义豆瓣图片代理 URL（当 DOUBAN_IMAGE_PROXY_TYPE=custom 时需要）
# NEXT_PUBLIC_DOUBAN_IMAGE_PROXY=https://your-douban-image-proxy.com

# ===========================================
# 内容过滤配置
# ===========================================
# 关闭色情内容过滤（true/false）
NEXT_PUBLIC_DISABLE_YELLOW_FILTER=false

# ===========================================
# Docker 运行时配置（通常不需要修改）
# ===========================================
# Docker 环境标识（Dockerfile 中已设置，通常不需要修改）
# DOCKER_ENV=true

# Node.js 环境（Dockerfile 中已设置，通常不需要修改）
# NODE_ENV=production

# 服务器主机名（Dockerfile 中已设置，通常不需要修改）
# HOSTNAME=0.0.0.0

# 服务端口（Dockerfile 中已设置，通常不需要修改）
# PORT=3000
