<div align="center">
<h1 align="center">Script Hub<h1>
</div>

<p align="center" color="#6a737d">
Advanced Script Converter for QX, Loon, Surge, Stash, Egern, LanceX and Shadowrocket
</p>
<p align="center" color="#6a737d">
重写 & 规则集转换
</p>


## 简介

• 支持将 QX 重写解析至 Surge Shadowrocket Loon Stash

• 支持将 Surge 模块解析至 Loon Stash

• 支持将 Loon 插件解析至 Surge Shadowrocket Stash

• 支持 QX & Surge & Loon & Shadowrocket & Clash 规则集解析，适用 app: Surge Shadowrocket Stash Loon

• 支持 将 QX 脚本转换成 Surge 脚本(兼容)

• 可以修改参数 argument

• 支持一键导入 Shadowrocket / Loon / Stash

• 高级功能 OR 修改任意文本

• 如果某些模块需要 `加参数才能使用` 但只想用远程链接，不想拉取到本地模块的情况 可以直接使用 `纯文本` -> `高级操作`、`修改参数` 功能修改远程链接 `任意内容` 或者 `argument` 参数, 不用再复制到本地模块

• [🆕 不需要代理 app 的全服务器部署版(测试中)](<https://github.com/Script-Hub-Org/Script-Hub/wiki/%E5%85%A8%E6%9C%8D%E5%8A%A1%E5%99%A8%E7%89%88(%E6%B5%8B%E8%AF%95%E4%B8%AD)>)

• 相关生态: [Surge 模块工具](https://github.com/Script-Hub-Org/Script-Hub/wiki/%E7%9B%B8%E5%85%B3%E7%94%9F%E6%80%81:-Surge-%E6%A8%A1%E5%9D%97%E5%B7%A5%E5%85%B7) 支持一键导入 Surge， 需要下载「Scriptable」app. 如果想把其他非 Script Hub 转换的 模块放在本地, 也可单独用此脚本

## 文档

[安装体验请查看文档](https://github.com/Script-Hub-Org/Script-Hub/wiki)
